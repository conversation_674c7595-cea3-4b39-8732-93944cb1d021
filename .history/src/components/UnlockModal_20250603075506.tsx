'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Elements, PaymentElement, useStripe, useElements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import { useAuth, useUser } from '@clerk/nextjs';
import { SignIn } from '@clerk/nextjs';
import { Button } from '@/components/ui/button';
import PRICING_CONFIG from '@/config/pricing';

// Simplified modal steps - maximum 3 steps
type ModalStep = 'selection' | 'payment' | 'success';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || '');

// Unified payment form component for both guest and authenticated users
const UnifiedPaymentForm = ({
  onSuccess,
  onError,
  email,
  userTokens = 0,
  onTokenUnlock,
}: {
  onSuccess: (paymentIntentId: string) => void;
  onError: (message: string) => void;
  email: string;
  userTokens?: number;
  onTokenUnlock?: () => void;
}) => {
  const stripe = useStripe();
  const elements = useElements();
  const [isProcessing, setIsProcessing] = useState(false);
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState<'token' | 'payment'>('payment');

  // If user has tokens, default to token payment
  React.useEffect(() => {
    if (userTokens > 0) {
      setPaymentMethod('token');
    }
  }, [userTokens]);

  const handleTokenUnlock = () => {
    if (onTokenUnlock) {
      onTokenUnlock();
    }
  };

  const handlePaymentSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      onError('Stripe has not loaded yet. Please try again.');
      return;
    }

    if (!agreedToTerms) {
      onError('Please agree to the terms and conditions');
      return;
    }

    setIsProcessing(true);

    try {
      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: window.location.origin,
          receipt_email: email,
        },
        redirect: 'if_required',
      });

      if (error) {
        throw new Error(error.message || 'Something went wrong with your payment');
      }

      if (paymentIntent && paymentIntent.status === 'succeeded') {
        onSuccess(paymentIntent.id);
      } else {
        throw new Error('Payment status unknown. Please contact support.');
      }
    } catch (err: unknown) {
      const error = err as Error;
      onError(error.message || 'Payment processing failed. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Payment method selection for users with tokens */}
      {userTokens > 0 && (
        <div className="space-y-3">
          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div className="flex justify-between items-center">
              <span className="text-blue-800 dark:text-blue-300 font-medium">Your Tokens</span>
              <span className="bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-300 px-3 py-1 rounded-full font-bold">
                {userTokens} {userTokens === 1 ? 'Token' : 'Tokens'}
              </span>
            </div>
          </div>

          <div className="flex space-x-2">
            <button
              type="button"
              onClick={() => setPaymentMethod('token')}
              className={`flex-1 py-2 px-4 rounded-lg border ${
                paymentMethod === 'token'
                  ? 'bg-blue-500 text-white border-blue-500'
                  : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600'
              }`}
            >
              Use 1 Token
            </button>
            <button
              type="button"
              onClick={() => setPaymentMethod('payment')}
              className={`flex-1 py-2 px-4 rounded-lg border ${
                paymentMethod === 'payment'
                  ? 'bg-blue-500 text-white border-blue-500'
                  : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600'
              }`}
            >
              Pay {PRICING_CONFIG.getFormattedPrice()}
            </button>
          </div>
        </div>
      )}

      {/* Token unlock option */}
      {paymentMethod === 'token' && userTokens > 0 && (
        <div className="text-center space-y-4">
          <p className="text-gray-600 dark:text-gray-400">
            Use 1 token to unlock this report's full details
          </p>
          <Button
            onClick={handleTokenUnlock}
            className="w-full bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] text-white"
          >
            Use 1 Token to Unlock
          </Button>
        </div>
      )}

      {/* Payment form */}
      {paymentMethod === 'payment' && (
        <form onSubmit={handlePaymentSubmit} className="space-y-6">
          <PaymentElement />

          <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Email</span>
            </div>
            <div className="text-gray-800 dark:text-gray-200 font-medium">
              {email}
            </div>
          </div>

          <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Payment Amount</span>
            </div>
            <div className="text-gray-800 dark:text-gray-200 font-bold">
              {PRICING_CONFIG.getFormattedPrice()}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              One-time payment, no subscription
            </div>
          </div>

          <div className="flex items-start space-x-2">
            <input
              id="terms"
              name="terms"
              type="checkbox"
              checked={agreedToTerms}
              onChange={(e) => setAgreedToTerms(e.target.checked)}
              className="mt-1 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            />
            <label htmlFor="terms" className="text-xs text-gray-500 dark:text-gray-400">
              I agree to the <a href="/terms-of-use" target="_blank" className="text-blue-600 dark:text-blue-400 hover:underline">Terms of Service</a> and <a href="/privacy-policy" target="_blank" className="text-blue-600 dark:text-blue-400 hover:underline">Privacy Policy</a>.
            </label>
          </div>

          <Button
            type="submit"
            disabled={isProcessing || !agreedToTerms}
            className="w-full"
          >
            {isProcessing ? (
              <span className="flex items-center justify-center">
                <svg className="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Processing Payment...
              </span>
            ) : (
              `Pay ${PRICING_CONFIG.getFormattedPrice()} to Unlock Results`
            )}
          </Button>

          <div className="text-xs text-center text-gray-500 dark:text-gray-400 mt-4">
            By proceeding, you agree to our Terms and Privacy Policy.
            Your payment is processed securely through Stripe.
          </div>
        </form>
      )}
    </div>
  );
};

// Simple email collection for guest users
const GuestEmailForm = ({
  onEmailSubmit,
  onError,
}: {
  onEmailSubmit: (email: string) => void;
  onError: (message: string) => void;
}) => {
  const [email, setEmail] = useState('');
  const [isValid, setIsValid] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || !/\S+@\S+\.\S+/.test(email)) {
      onError('Please enter a valid email address');
      return;
    }

    onEmailSubmit(email);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Email Address
        </label>
        <input
          type="email"
          id="email"
          value={email}
          onChange={(e) => {
            setEmail(e.target.value);
            setIsValid(/\S+@\S+\.\S+/.test(e.target.value));
          }}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md
                    shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500
                    focus:border-blue-500 dark:bg-gray-800 dark:text-white"
          placeholder="<EMAIL>"
          required
        />
        <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
          We'll email you access to your search results
        </p>
      </div>

      <div className="text-center font-medium">
        <div className="mb-2 text-gray-700 dark:text-gray-300">
          One-Time Payment
        </div>
        <div className="text-2xl text-transparent bg-clip-text bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF]">
          {PRICING_CONFIG.getFormattedPrice()}
        </div>
      </div>

      <Button
        type="submit"
        disabled={!isValid}
        className="w-full"
      >
        Continue to Payment
      </Button>
    </form>
  );
};

// PhoneVerification component for verifying phone numbers
const PhoneVerification = ({
  onVerificationComplete,
  onError,
  phoneNumber,
}: {
  onVerificationComplete: () => void;
  onError: (message: string) => void;
  phoneNumber: string;
}) => {
  const { isLoaded, signUp, setActive } = useSignUp();
  const [verificationCode, setVerificationCode] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [isVerified, setIsVerified] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isLoaded) return;

    if (!verificationCode) {
      setFormError('Verification code is required');
      return;
    }

    setIsSubmitting(true);
    setFormError(null);

    try {
      // Attempt to verify the phone number
      const result = await signUp.attemptPhoneNumberVerification({
        code: verificationCode,
      });

      // Set the user as active - this completes the sign-up process
      await setActive({ session: result.createdSessionId });
      
      // Wait a moment for the auth state to update
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // Award 5 tokens to the new user after successful verification
      try {
        const response = await fetch('/api/data?action=award-tokens', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ tokens: 5, reason: 'initial_signup' }),
        });
        
        if (!response.ok) {
          console.error('Error awarding tokens:', await response.text());
        } else {
          console.log('Successfully awarded 5 initial tokens');
        }
      } catch (tokenError) {
        console.error('Failed to award tokens:', tokenError);
        // Continue even if token awarding fails - don't block the user
      }
      
      // Show verification success screen
      setIsVerified(true);
    } catch (err: any) {
      console.error('Verification error:', err);
      setFormError(err.errors?.[0]?.message || 'Failed to verify phone number. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleResendCode = async () => {
    if (!isLoaded) return;

    try {
      // Resend verification code
      await signUp.preparePhoneNumberVerification();
      console.log('Verification code resent');
    } catch (err: any) {
      console.error('Error resending verification code:', err);
      setFormError(err.errors?.[0]?.message || 'Failed to resend verification code. Please try again.');
    }
  };

  // Phone verification success screen
  if (isVerified) {
    return (
      <div className="text-center space-y-6">
        <div className="w-20 h-20 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-6">
          <motion.svg
            className="w-10 h-10 text-green-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
            initial={{ opacity: 0, pathLength: 0 }}
            animate={{ opacity: 1, pathLength: 1 }}
            transition={{ duration: 0.8, ease: "easeInOut" }}
          >
            <motion.path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M5 13l4 4L19 7"
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              transition={{ duration: 0.8, ease: "easeInOut" }}
            ></motion.path>
          </motion.svg>
        </div>
        
        <h3 className="text-xl font-bold text-gray-900 dark:text-white">
          Phone Verified!
        </h3>
        
        <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <p className="text-gray-800 dark:text-gray-200 mb-2">
            Your account has been created successfully.
          </p>
          <p className="text-blue-700 dark:text-blue-300 font-semibold">
            5 tokens have been added to your account! 🎉
          </p>
        </div>
        
        <Button 
          onClick={onVerificationComplete}
          className="w-full bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] text-white"
        >
          Unlock Report
        </Button>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <div className="mb-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg text-sm text-blue-800 dark:text-blue-300">
          <p className="font-medium">Verification code sent to:</p>
          <p className="font-bold">{phoneNumber}</p>
        </div>
        
        <label htmlFor="verificationCode" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Enter Verification Code
        </label>
        <input
          type="text"
          id="verificationCode"
          value={verificationCode}
          onChange={(e) => setVerificationCode(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md 
                    shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 
                    focus:border-blue-500 dark:bg-gray-800 dark:text-white tracking-widest text-center"
          placeholder="000000"
          maxLength={6}
          required
        />
      </div>

      {formError && (
        <div className="p-3 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 text-sm rounded">
          {formError}
        </div>
      )}

      <div className="flex flex-col space-y-3">
        <Button 
          type="submit" 
          disabled={isSubmitting || !isLoaded || verificationCode.length < 4}
          className="w-full"
        >
          {isSubmitting ? 'Verifying...' : 'Verify Phone Number'}
        </Button>
        
        <Button 
          type="button" 
          onClick={handleResendCode}
          disabled={isSubmitting}
          className="w-full bg-white text-blue-600 border border-blue-200 hover:bg-blue-50 dark:bg-gray-800 dark:text-blue-400 dark:border-blue-800 dark:hover:bg-gray-700"
        >
          Resend Verification Code
        </Button>
      </div>
    </form>
  );
};

// Success screen shown after payment completion or signup
const SuccessScreen = ({ 
  onClose, 
  message = "Your results are now available!" 
}: { 
  onClose: () => void, 
  message?: string 
}) => {
  const [countdown, setCountdown] = useState(3);

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          onClose();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [onClose]);

  return (
    <div className="text-center p-6">
      <div className="w-20 h-20 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-6">
        <svg
          className="w-10 h-10 text-green-500"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M5 13l4 4L19 7"
          ></path>
        </svg>
      </div>
      <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
        Success!
      </h3>
      <p className="text-gray-600 dark:text-gray-300 mb-4">
        {message}
      </p>
      <p className="text-sm text-gray-500 dark:text-gray-400">
        Closing automatically in {countdown} seconds
      </p>
    </div>
  );
};

// Main UnlockModal component
export default function UnlockModal({
  isOpen,
  onClose,
  reportId,
  reportIdentifier,
}: {
  isOpen: boolean;
  onClose: (success: boolean) => void;
  reportId: number;
  reportIdentifier: string;
}) {
  const router = useRouter();
  const { isLoaded: isAuthLoaded, userId } = useAuth();
  const { user, isLoaded: isUserLoaded } = useUser();
  const { signIn } = useSignIn();
  
  // Add state to track tokens
  const [userTokens, setUserTokens] = useState<number>(0);
  const [isTokenLoading, setIsTokenLoading] = useState<boolean>(false);
  
  // State for the current modal step
  const [currentStep, setCurrentStep] = useState<ModalStep>('selection');
  
  // Guest checkout state
  const [guestEmail, setGuestEmail] = useState<string>('');
  const [isGuestEmailValid, setIsGuestEmailValid] = useState<boolean>(false);
  
  // Phone verification state
  const [phoneNumber, setPhoneNumber] = useState<string>('');
  
  // Payment state
  const [clientSecret, setClientSecret] = useState<string>('');
  const [isCreatingPaymentIntent, setIsCreatingPaymentIntent] = useState<boolean>(false);
  
  // Error handling
  const [error, setError] = useState<string | null>(null);
  
  // Track if the user is a new user (for free token flow)
  const [isNewUser, setIsNewUser] = useState<boolean>(false);
  
  // Reference to the Stripe instance
  const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || '');

  // Log reportId when the component mounts or reportId changes
  useEffect(() => {
    console.log(`UnlockModal: Received reportId:`, reportIdentifier, `type:`, typeof reportIdentifier);
  }, [reportIdentifier]);

  // When the modal opens, determine the initial step and fetch user data
  useEffect(() => {
    if (isOpen) {
      setError(null);
      
      // Reset the state
      setCurrentStep('selection');
      setGuestEmail('');
      setIsGuestEmailValid(false);
      setPhoneNumber('');
      setClientSecret('');
      
      // Debug the reportId
      console.log(`UnlockModal opened with reportId:`, reportId, `type:`, typeof reportId);
      console.log(`UnlockModal opened with reportIdentifier:`, reportIdentifier, `type:`, typeof reportIdentifier);
      if (!reportIdentifier) {
        console.error('Warning: reportIdentifier missing or invalid!');
      }

      // Check auth status and fetch data if authenticated
      if (isAuthLoaded && userId) {
        console.log('Modal opened, user authenticated. Fetching tokens...');
        fetchUserTokens(); // Call fetchUserTokens here
      } else if (isAuthLoaded && !userId) {
        console.log('Modal opened, user not authenticated.');
        setCurrentStep('selection'); // Ensure selection step for unauthenticated users
        setUserTokens(0); // Reset tokens if user is not logged in
      } else {
        console.log('Modal opened, auth state not loaded yet.');
        // Optionally show a loading state or wait for isAuthLoaded
      }
    } else {
      // Optional: Reset state when modal closes
      // setError(null);
      // setUserTokens(0);
      // setCurrentStep('selection');
    }
  }, [isOpen, isAuthLoaded, userId]); // Dependencies: run when modal opens or auth state changes

  // When user data or tokens change, determine the best step
  useEffect(() => {
    // This effect reacts to changes in auth state and token count *after* fetchUserTokens completes
    if (isOpen && isAuthLoaded && userId) {
      // User is authenticated
      if (!isTokenLoading) { // Only update step after loading is complete
        if (userTokens > 0) {
          console.log(`User has ${userTokens} tokens. Setting step to tokenUnlock.`);
          setCurrentStep('tokenUnlock');
        } else {
          console.log('User has 0 tokens. Setting step to authUserPayment.');
          setCurrentStep('authUserPayment');
        }
      } else {
        console.log('Tokens are loading, waiting to set step...');
        // Optionally set a loading step here if needed
        // setCurrentStep('loadingTokens');
      }
    } else if (isOpen && isAuthLoaded && !userId) {
      // User is not authenticated, ensure selection screen
      console.log('User not authenticated. Ensuring selection step.');
      setCurrentStep('selection');
    }
    // No need to depend on fetchUserTokens directly, it's triggered by the other useEffect
  }, [isOpen, isAuthLoaded, userId, userTokens, isTokenLoading]); // Dependencies: run when auth/token state changes

  // Fetch user tokens from the API
  const fetchUserTokens = async () => {
    if (!userId) return; // Guard clause if userId is not available

    setIsTokenLoading(true);
    setError(null); // Clear previous errors

    try {
      const response = await fetch('/api/data?action=user-data');

      if (!response.ok) {
        // Attempt to get error message from response body
        let errorMsg = 'Failed to fetch user data';
        try {
          const errorData = await response.json();
          errorMsg = errorData.error || errorMsg;
        } catch (parseError) {
          // Ignore if response body is not JSON or empty
        }
        throw new Error(errorMsg);
      }

      const data = await response.json();

      // The API doesn't return data.success && data.users structure
      // Instead, it directly returns tokens and loggedIn properties
      console.log('User data response:', data);
      
      if (data.tokens !== undefined) {
        setUserTokens(data.tokens); // Get tokens directly from the response
        
          // The API doesn't return created_at directly, so we'll need to check if isNewUser needs to be set
          // For now, we'll keep this commented and add it if needed later
          // if (data.created_at) {
          //   const createdAt = new Date(data.created_at);
          //   const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
          //   setIsNewUser(createdAt > thirtyMinutesAgo);
          // }
      } else if (data.error) {
        // Handle API error responses
        throw new Error(data.error);
      } else {
        // If no tokens property but no error either, assume 0 tokens
        console.log('No tokens data found in response, defaulting to 0');
        setUserTokens(0);
      }
    } catch (error: any) { // Catch any error type
      console.error('Error fetching user tokens:', error);
      setError(error.message || 'Failed to fetch user data. Please try again.');
      setUserTokens(0); // Reset tokens on error
    } finally {
      setIsTokenLoading(false);
    }
  };
  
  // Create a payment intent for the guest or auth user
  const createPaymentIntent = async (userEmail: string) => {
    setIsCreatingPaymentIntent(true);
    setError(null);
    
    if (!reportIdentifier && !reportId) {
      setError('Report ID is missing. Please try again.');
      console.error('Error in createPaymentIntent: both reportIdentifier and reportId are undefined or null', { reportIdentifier, reportId });
      return;
    }
    
    // Prioritize reportIdentifier over reportId
    const idToUse = reportIdentifier || String(reportId);
    
    try {
      // Determine service type based on auth status
      const serviceType = userId ? 'report_unlock_auth' : 'report_unlock_guest';
      
      const response = await fetch('/api/core?action=create-payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: userEmail,
          reportId: idToUse,
          serviceType,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create payment intent');
      }
      
      const data = await response.json();
      
      if (data.clientSecret) {
        setClientSecret(data.clientSecret);
        return data.clientSecret;
      } else {
        throw new Error('No client secret returned');
      }
    } catch (error: any) {
      console.error('Error creating payment intent:', error);
      setError(error.message || 'Failed to create payment intent. Please try again.');
      throw error;
    } finally {
      setIsCreatingPaymentIntent(false);
    }
  };
  
  // Handle unlocking with a token
  const handleTokenUnlock = async () => {
    if (!userId || userTokens <= 0) {
      setError('You don\'t have any tokens available.');
      return;
    }
    
    if (!reportIdentifier && !reportId) {
      setError('Report ID is missing. Please try again.');
      console.error('Error in handleTokenUnlock: both reportIdentifier and reportId are undefined or null', { reportIdentifier, reportId });
      return;
    }
    
    // Prioritize reportIdentifier over reportId
    const idToUse = reportIdentifier || String(reportId);
    
    setError(null);
    console.log(`Attempting to unlock report with URL ID ${reportIdentifier} and DB ID ${reportId}`);
    
    try {
      // Make request to unlock API endpoint using the URL path identifier
      const response = await fetch('/api/reports/unlock-with-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reportId: idToUse, // Use the URL path ID (string) or fall back to database ID
        }),
      });
      
      console.log(`API response status:`, response.status, response.statusText);
      
      // Clone the response and get text for logging
      const responseClone = response.clone();
      const responseText = await responseClone.text();
      console.log('Raw response:', responseText);
      
      // Parse the response text
      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Failed to parse response:', parseError);
        throw new Error('Invalid response from server');
      }
      
      // Check for error status
      if (!response.ok) {
        throw new Error(data.error || 'Failed to unlock report with token');
      }
      
      if (data.success) {
        // Update token count locally
        setUserTokens(prevTokens => Math.max(0, prevTokens - 1));
        
        // Show success message and close modal
        setCurrentStep('success');
        
        // After a brief delay, close the modal with success
        setTimeout(() => {
          onClose(true);
        }, 2000);
      } else {
        throw new Error(data.error || 'Failed to unlock report with token');
      }
    } catch (error: any) {
      console.error('Error unlocking with token:', error);
      setError(error.message || 'Failed to unlock report with token. Please try again.');
    }
  };
  
  // Handle unlocking for a new user (free)
  const handleNewUserUnlock = async () => {
    if (!userId) {
      setError('You must be signed in to unlock this report.');
      return;
    }
    
    if (!reportIdentifier && !reportId) {
      setError('Report ID is missing. Please try again.');
      console.error('Error in handleNewUserUnlock: both reportIdentifier and reportId are undefined or null', { reportIdentifier, reportId });
      return;
    }
    
    // Prioritize reportIdentifier over reportId
    const idToUse = reportIdentifier || String(reportId);
    
    setError(null);
    console.log(`Attempting to unlock report for new user with URL ID ${reportIdentifier} and DB ID ${reportId}`);
    
    try {
      const response = await fetch('/api/reports/unlock-for-new-user', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reportId: idToUse, // Use the URL path ID (string) or fall back to database ID
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to unlock report for new user');
      }
      
      const data = await response.json();
      
      if (data.success) {
        // Show success message and close modal
        setCurrentStep('success');
        
        // After a brief delay, close the modal with success
        setTimeout(() => {
          onClose(true);
        }, 2000);
      } else {
        throw new Error(data.error || 'Failed to unlock report for new user');
      }
    } catch (error: any) {
      console.error('Error unlocking for new user:', error);
      setError(error.message || 'Failed to unlock report for new user. Please try again.');
    }
  };
  
  // Handle unlocking with a payment
  const handlePaymentUnlock = async (paymentIntentId: string) => {
    if (!paymentIntentId) {
      setError('No payment information available.');
      return;
    }
    
    if (!reportIdentifier && !reportId) {
      setError('Report ID is missing. Please try again.');
      console.error('Error in handlePaymentUnlock: both reportIdentifier and reportId are undefined or null', { reportIdentifier, reportId });
      return;
    }
    
    // Prioritize reportIdentifier over reportId
    const idToUse = reportIdentifier || String(reportId);
    
    setError(null);
    
    try {
      const response = await fetch('/api/reports/unlock', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reportId: idToUse,
          paymentIntentId,
          email: guestEmail,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to unlock report with payment');
      }
      
      const data = await response.json();
      
      if (data.success) {
        // Show success message and close modal
        setCurrentStep('success');
        
        // After a brief delay, close the modal with success
        setTimeout(() => {
          onClose(true);
        }, 2000);
      } else {
        throw new Error(data.error || 'Failed to unlock report with payment');
      }
    } catch (error: any) {
      console.error('Error unlocking with payment:', error);
      setError(error.message || 'Failed to unlock report with payment. Please try again.');
    }
  };
  
  // Handle guest email form submission
  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic email validation
    if (!guestEmail) {
      setError('Email is required');
      return;
    }
    
    if (!/\S+@\S+\.\S+/.test(guestEmail)) {
      setError('Please enter a valid email');
      return;
    }
    
    setError(null);
    
    // Proceed to payment step for guest
    try {
      await createPaymentIntent(guestEmail);
      setCurrentStep('guestPayment');
    } catch (error) {
      // Error is already handled in createPaymentIntent
    }
  };
  
  // Handle sign up completion - proceed to phone verification
  const handleSignUpComplete = (phone: string) => {
    setPhoneNumber(phone);
    setCurrentStep('verifyPhone');
  };
  
  // Handle phone verification completion
  const handleVerificationComplete = () => {
    // After verification, we know the user is now authenticated
    // We need to check if they have tokens and decide what to do next
    fetchUserTokens();
    
    // Move to the new user unlock flow
    // The useEffect will update based on tokens later if needed
    setCurrentStep('success');
    handleNewUserUnlock();
  };
  
  // Handle closing the modal
  const handleCloseModal = () => {
    onClose(false);
  };
  
  // Don't render anything if the modal is closed
  if (!isOpen) {
    return null;
  }
  
  // Modal backdrop and container
  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <motion.div
        className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-md w-full overflow-hidden relative"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
      >
        {/* Close button */}
        <button
          onClick={handleCloseModal}
          className="absolute top-3 right-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 z-10"
          aria-label="Close"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        
        {/* Modal content */}
        <div className="p-6">
          {/* Modal header */}
          <div className="text-center mb-6">
            <div className="h-12 w-12 mx-auto mb-4 rounded-full flex items-center justify-center bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF]">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">Unlock Full Results</h2>
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              {currentStep === 'tokenUnlock' ? 'Use a token to unlock' :
               currentStep === 'authUserPayment' ? 'Complete payment to unlock' :
               currentStep === 'guestPayment' ? 'Complete payment to unlock' :
               currentStep === 'selection' ? 'Choose how to unlock' :
               currentStep === 'signIn' ? 'Sign in to your account' :
               currentStep === 'signUp' ? 'Create an account' :
               currentStep === 'verifyPhone' ? 'Verify your phone number' :
               currentStep === 'success' ? 'Report unlocked successfully!' : ''}
            </p>
          </div>
          
          {/* Error message */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg text-red-600 dark:text-red-400 text-sm">
              <div className="flex items-center">
                <svg className="w-5 h-5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <span>{error}</span>
              </div>
            </div>
          )}
          
          {/* Step content */}
          {currentStep === 'selection' && (
            <div className="space-y-4">
              <div className="text-center mb-4">
                <div className="flex justify-center gap-2 text-2xl mb-2">
                  🔍 👤 🌐
                </div>
                <p className="text-gray-700 dark:text-gray-300">
                  View complete source details and unlock full access to this report
                </p>
              </div>
              
              <div className="space-y-3">
                <button
                  onClick={() => setCurrentStep('guestEmail')}
                  className="w-full py-3 px-4 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-800 dark:text-white font-medium hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors flex items-center justify-center"
                >
                  <svg className="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                  </svg>
                  Pay as Guest ($5.00)
                </button>
                
                <div className="flex items-center">
                  <div className="flex-1 h-px bg-gray-200 dark:bg-gray-700"></div>
                  <span className="px-3 text-gray-500 dark:text-gray-400 text-sm">OR</span>
                  <div className="flex-1 h-px bg-gray-200 dark:bg-gray-700"></div>
                </div>
                
                <button
                  onClick={() => setCurrentStep('signIn')}
                  className="w-full py-3 px-4 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-800 dark:text-white font-medium hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors flex items-center justify-center"
                >
                  <svg className="w-5 h-5 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  Sign In
                </button>
                
                <button
                  onClick={() => setCurrentStep('signUp')}
                  className="w-full py-3 px-4 bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] rounded-lg text-white font-medium hover:opacity-90 transition-colors flex items-center justify-center"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                  </svg>
                  Create Account (5 Free Tokens)
                </button>
              </div>
            </div>
          )}
          
          {currentStep === 'guestEmail' && (
            <form onSubmit={handleEmailSubmit} className="space-y-4">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Email
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  placeholder="<EMAIL>"
                  value={guestEmail}
                  onChange={(e) => {
                    setGuestEmail(e.target.value);
                    setIsGuestEmailValid(/\S+@\S+\.\S+/.test(e.target.value));
                  }}
                  className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600"
                  required
                />
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  We'll email you access to your search results
                </p>
              </div>
              
              <div className="text-center font-medium">
                <div className="mb-2 text-gray-700 dark:text-gray-300">
                  One-Time Payment
                </div>
                <div className="text-2xl text-transparent bg-clip-text bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF]">
                  $5.00
                </div>
              </div>
              
              <Button 
                type="submit" 
                disabled={isCreatingPaymentIntent || !isGuestEmailValid}
                className="w-full"
              >
                {isCreatingPaymentIntent ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing...
                  </span>
                ) : (
                  'Continue to Payment'
                )}
              </Button>
              
              <div className="text-center">
                <button
                  type="button"
                  onClick={() => setCurrentStep('selection')}
                  className="text-blue-600 dark:text-blue-400 text-sm hover:underline"
                >
                  Go back
                </button>
              </div>
            </form>
          )}
          
          {currentStep === 'guestPayment' && clientSecret && (
            <div>
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Email</span>
                </div>
                <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <span className="text-gray-800 dark:text-gray-200 font-medium">{guestEmail}</span>
                </div>
              </div>
              
              <Elements stripe={stripePromise} options={{ clientSecret }}>
                <CheckoutForm
                  email={guestEmail}
                  onSuccess={(paymentIntentId) => handlePaymentUnlock(paymentIntentId)}
                  onError={(message) => setError(message)}
                />
              </Elements>
              
              <div className="text-center mt-4">
                <button
                  type="button"
                  onClick={() => setCurrentStep('guestEmail')}
                  className="text-blue-600 dark:text-blue-400 text-sm hover:underline"
                >
                  Go back
                </button>
              </div>
            </div>
          )}
          
          {currentStep === 'tokenUnlock' && (
            <div className="space-y-4">
              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="flex justify-between items-center">
                  <span className="text-blue-800 dark:text-blue-300 font-medium">Your Tokens</span>
                  <span className="bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-300 px-3 py-1 rounded-full font-bold">
                    {isTokenLoading ? (
                      <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    ) : (
                      `${userTokens} ${userTokens === 1 ? 'Token' : 'Tokens'}`
                    )}
                  </span>
                </div>
              </div>
              
              <div className="text-center space-y-2">
                <p className="text-gray-600 dark:text-gray-400">
                  Use 1 token to unlock this report's full details
                </p>
                
                <Button
                  onClick={handleTokenUnlock}
                  disabled={isTokenLoading || userTokens <= 0}
                  className="w-full"
                >
                  Use 1 Token to Unlock
                </Button>
                
                <div className="flex items-center mt-2">
                  <div className="flex-1 h-px bg-gray-200 dark:bg-gray-700"></div>
                  <span className="px-3 text-gray-500 dark:text-gray-400 text-sm">OR</span>
                  <div className="flex-1 h-px bg-gray-200 dark:bg-gray-700"></div>
                </div>
                
                <Button
                  onClick={() => setCurrentStep('authUserPayment')}
                  className="w-full bg-white text-blue-600 border border-blue-200 hover:bg-blue-50 dark:bg-gray-800 dark:text-blue-400 dark:border-blue-800 dark:hover:bg-gray-700"
                >
                  Pay $5 Instead
                </Button>
                
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                  You'll be redirected to our secure payment page
                </div>
              </div>
            </div>
          )}
          
          {currentStep === 'authUserPayment' && (
            <div className="space-y-4">
              <div className="text-center mb-4">
                <p className="text-gray-700 dark:text-gray-300">
                  {isTokenLoading ? (
                    'Checking your available tokens...'
                  ) : userTokens === 0 ? (
                    'You have no tokens available. Purchase this report for $5.'
                  ) : (
                    'Purchase this report instead of using tokens.'
                  )}
                </p>
              </div>
              
              <div className="text-center font-medium">
                <div className="mb-2 text-gray-700 dark:text-gray-300">
                  One-Time Payment
                </div>
                <div className="text-2xl text-transparent bg-clip-text bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF]">
                  $5.00
                </div>
              </div>
              
              <Button
                onClick={async () => {
                  try {
                    await createPaymentIntent(user?.primaryEmailAddress?.emailAddress || '');
                    setCurrentStep('authUserPaymentConfirm');
                  } catch (error) {
                    // Error is already handled in createPaymentIntent
                  }
                }}
                disabled={isCreatingPaymentIntent}
                className="w-full"
              >
                {isCreatingPaymentIntent ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing...
                  </span>
                ) : (
                  'Continue to Payment'
                )}
              </Button>
              
              {userTokens > 0 && (
                <div className="text-center">
                  <button
                    type="button"
                    onClick={() => setCurrentStep('tokenUnlock')}
                    className="text-blue-600 dark:text-blue-400 text-sm hover:underline"
                  >
                    Go back to token option
                  </button>
                </div>
              )}
            </div>
          )}
          
          {currentStep === 'authUserPaymentConfirm' && clientSecret && (
            <div>
              <Elements stripe={stripePromise} options={{ clientSecret }}>
                <CheckoutForm
                  email={user?.primaryEmailAddress?.emailAddress || ''}
                  onSuccess={(paymentIntentId) => handlePaymentUnlock(paymentIntentId)}
                  onError={(message) => setError(message)}
                />
              </Elements>
              
              <div className="text-center mt-4">
                <button
                  type="button"
                  onClick={() => {
                    if (userTokens > 0) {
                      setCurrentStep('tokenUnlock');
                    } else {
                      setCurrentStep('authUserPayment');
                    }
                  }}
                  className="text-blue-600 dark:text-blue-400 text-sm hover:underline"
                >
                  Go back
                </button>
              </div>
            </div>
          )}
          
          {currentStep === 'signIn' && (
            <div>
              <SignIn 
                afterSignInUrl={window.location.href}
                signUpUrl=""
                routing="virtual"
                redirectUrl={window.location.href}
                appearance={{
                  variables: {
                    colorPrimary: '#92A5FF',
                  },
                }}
              />
              
              <div className="text-center mt-4">
                <button
                  type="button"
                  onClick={() => setCurrentStep('selection')}
                  className="text-blue-600 dark:text-blue-400 text-sm hover:underline"
                >
                  Go back
                </button>
              </div>
            </div>
          )}
          
          {currentStep === 'signUp' && (
            <SignUpForm
              onSignUpComplete={handleSignUpComplete}
              onError={(message) => setError(message)}
            />
          )}
          
          {currentStep === 'verifyPhone' && (
            <PhoneVerification
              onVerificationComplete={handleVerificationComplete}
              onError={(message) => setError(message)}
              phoneNumber={phoneNumber}
            />
          )}
          
          {currentStep === 'newUserFreeUnlock' && (
            <div className="space-y-4 text-center">
              <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <p className="text-green-800 dark:text-green-300 font-semibold">Welcome! Your first report unlock is completely free.</p>
                <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
                  As a new user, you can unlock one report without using any tokens or making a payment.
                </p>
              </div>
              
              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="flex justify-between items-center">
                  <span className="text-blue-800 dark:text-blue-300 font-medium">Your Tokens</span>
                  <span className="bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-300 px-3 py-1 rounded-full font-bold">
                    {isTokenLoading ? (
                      <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    ) : (
                      `${userTokens} ${userTokens === 1 ? 'Token' : 'Tokens'}`
                    )}
                  </span>
                </div>
              </div>
              
              <Button
                onClick={handleNewUserUnlock}
                disabled={isTokenLoading}
                className="w-full bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] text-white"
              >
                Unlock Report (Free)
              </Button>
              
              <div className="flex items-center mt-2">
                <div className="flex-1 h-px bg-gray-200 dark:bg-gray-700"></div>
                <span className="px-3 text-gray-500 dark:text-gray-400 text-sm">OR</span>
                <div className="flex-1 h-px bg-gray-200 dark:bg-gray-700"></div>
              </div>
              
              <Button
                onClick={() => {
                  if (userTokens > 0) {
                    setCurrentStep('tokenUnlock');
                  } else {
                    setCurrentStep('authUserPayment');
                  }
                }}
                className="w-full bg-white text-blue-600 border border-blue-200 hover:bg-blue-50 dark:bg-gray-800 dark:text-blue-400 dark:border-blue-800 dark:hover:bg-gray-700"
              >
                Use Other Payment Method
              </Button>
            </div>
          )}
          
          {currentStep === 'success' && (
            <SuccessScreen
              onClose={() => onClose(true)}
              message="Your report has been unlocked successfully!"
            />
          )}
          
          {/* Security info at bottom */}
          <div className="flex items-center justify-center mt-4 text-xs text-gray-500 dark:text-gray-400">
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
            Secure payment processed by Stripe
          </div>
        </div>
      </motion.div>
    </div>
  );
}
