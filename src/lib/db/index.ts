// src/lib/db/index.ts
import { drizzle } from 'drizzle-orm/neon-http';
import { neon } from '@neondatabase/serverless';
import { eq, sql } from 'drizzle-orm';
import * as schema from './schema'; // Import the entire schema
import * as types from './types'; // Import types

// Use types from the types.ts file
type User = types.User;
type UserInsert = types.UserInsert;

type GuestTransaction = types.GuestTransaction;
type GuestTransactionInsert = types.GuestTransactionInsert;

type ReportTransaction = types.ReportTransaction;
type ReportTransactionInsert = types.ReportTransactionInsert;

type SearchReport = types.SearchReport;
type SearchReportInsert = types.SearchReportInsert;

type Upload = types.Upload;
type UploadInsert = types.UploadInsert;

type SearchResultRecord = types.SearchResultRecord;
type SearchResultInsert = types.SearchResultInsert;

// Database connection setup
if (!process.env.DATABASE_URL) {
  throw new Error('DATABASE_URL is not set');
}

// Use the proper type casting for neon client
const sqlDb = neon(process.env.DATABASE_URL!) as any;

// Initialize Drizzle with custom type mapping for decimals
export const db = drizzle(sqlDb, {
  schema,
});

// --- Guest Transactions ---

export async function createGuestTransaction(data: Omit<GuestTransactionInsert, 'id' | 'createdAt' | 'updatedAt'>): Promise<GuestTransaction> {
  const result = await db.insert(schema.guestTransactions).values({
    ...data,
    amount: String(data.amount), // Ensure amount is string for numeric type
  }).returning();
  return result[0];
}

export async function updateGuestTransaction(id: number, data: Partial<Omit<GuestTransactionInsert, 'id' | 'createdAt'>>): Promise<GuestTransaction | null> {
  const updateData = { ...data };
  if (updateData.amount !== undefined) {
    updateData.amount = String(updateData.amount); // Ensure amount is string
  }
  const result = await db.update(schema.guestTransactions)
    .set({ ...updateData, updatedAt: sql`now()` })
    .where(eq(schema.guestTransactions.id, id))
    .returning();
  return result.length > 0 ? result[0] : null;
}

export async function getGuestTransactionBySessionId(sessionId: string): Promise<GuestTransaction | null> {
  const result = await db.select().from(schema.guestTransactions).where(eq(schema.guestTransactions.stripeSessionId, sessionId));
  return result.length > 0 ? result[0] : null;
}

export async function getGuestTransactionByPaymentId(paymentId: string): Promise<GuestTransaction | null> {
    const result = await db.select().from(schema.guestTransactions).where(eq(schema.guestTransactions.stripePaymentId, paymentId));
    return result.length > 0 ? result[0] : null;
}

// --- Report Transactions (User) ---

export async function createReportTransaction(data: Omit<ReportTransactionInsert, 'id' | 'createdAt' | 'updatedAt'>): Promise<ReportTransaction> {
    const result = await db.insert(schema.reportTransactions).values({
        ...data,
    }).returning();
    return result[0];
}

export async function updateReportTransaction(id: number, data: Partial<Omit<ReportTransactionInsert, 'id' | 'createdAt'>>): Promise<ReportTransaction | null> {
    const updateData = { ...data };
    if (updateData.amount !== undefined) {
      updateData.amount = String(updateData.amount); // Ensure amount is string
    }
    const result = await db.update(schema.reportTransactions)
      .set({ ...updateData, updatedAt: sql`now()` })
      .where(eq(schema.reportTransactions.id, id))
      .returning();
    return result.length > 0 ? result[0] : null;
}

export async function getReportTransactionBySessionId(sessionId: string): Promise<ReportTransaction | null> {
    const result = await db.select().from(schema.reportTransactions).where(eq(schema.reportTransactions.stripeSessionId, sessionId));
    return result.length > 0 ? result[0] : null;
}

export async function getReportTransactionByPaymentId(paymentId: string): Promise<ReportTransaction | null> {
    const result = await db.select().from(schema.reportTransactions).where(eq(schema.reportTransactions.stripePaymentId, paymentId));
    return result.length > 0 ? result[0] : null;
}


// --- Search Reports ---

export async function createSearchReport(data: Omit<SearchReportInsert, 'id' | 'createdAt'>): Promise<SearchReport> {
    const result = await db.insert(schema.searchReports).values({
        ...data,
        price: data.price !== undefined && data.price !== null ? String(data.price) : null,
    }).returning();
    return result[0];
}

export async function updateSearchReport(reportId: number, data: Partial<Omit<SearchReportInsert, 'id' | 'createdAt'>>): Promise<SearchReport | null> {
    const updateData = { ...data };
    if (updateData.price !== undefined) {
        updateData.price = updateData.price !== null ? String(updateData.price) : null;
    }
    const result = await db.update(schema.searchReports)
        .set(updateData) // updatedAt is not in schema for searchReports
        .where(eq(schema.searchReports.id, reportId))
        .returning();
    return result.length > 0 ? result[0] : null;
}

export async function getSearchReportById(reportId: number | string): Promise<SearchReport | null> {
    const id = typeof reportId === 'string' ? parseInt(reportId, 10) : reportId;
    const result = await db.select().from(schema.searchReports).where(eq(schema.searchReports.id, id));
    return result.length > 0 ? result[0] : null;
}

export async function getSearchReportsByUserId(userId: number): Promise<SearchReport[]> {
    return await db.select().from(schema.searchReports).where(eq(schema.searchReports.userId, userId));
}

export async function getSearchReportByReportId(reportId: string): Promise<SearchReport | null> {
    // Find the report associated with the given report_id
    const result = await db.select().from(schema.searchReports).where(eq(schema.searchReports.report_id, reportId));
    return result.length > 0 ? result[0] : null;
}

// --- Uploads ---

export async function createUpload(data: Omit<UploadInsert, 'id' | 'createdAt' | 'updatedAt'>): Promise<Upload> {
    const result = await db.insert(schema.uploads).values(data).returning();
    return result[0];
}

export async function updateUpload(id: number, data: Partial<Omit<UploadInsert, 'id' | 'createdAt'>>): Promise<Upload | null> {
    const result = await db.update(schema.uploads)
        .set({ ...data, updatedAt: sql`now()` })
        .where(eq(schema.uploads.id, id))
        .returning();
    return result.length > 0 ? result[0] : null;
}

export async function getUploadsBySearchReportId(reportId: number): Promise<Upload[]> {
    return await db.select().from(schema.uploads).where(eq(schema.uploads.search_report_id, reportId));
}

export async function getUploadById(id: number): Promise<Upload | null> {
    const result = await db.select().from(schema.uploads).where(eq(schema.uploads.id, id));
    return result.length > 0 ? result[0] : null;
}

// --- Search Results ---

export async function createSearchResult(data: Omit<SearchResultInsert, 'id' | 'createdAt' | 'updatedAt'>): Promise<SearchResultRecord> {
    const result = await db.insert(schema.searchResults).values({
        ...data,
        confidence: data.confidence !== undefined ? String(data.confidence) : null,
    }).returning();
    return result[0];
}

export async function getSearchResultsByReportId(reportId: number): Promise<SearchResultRecord[]> {
    return await db.select().from(schema.searchResults)
        .where(eq(schema.searchResults.searchReportId, reportId));
}

export async function getSearchResultsByUploadId(uploadId: number): Promise<SearchResultRecord[]> {
    return await db.select().from(schema.searchResults)
        .where(eq(schema.searchResults.uploadId, uploadId));
}

export async function getSearchResultById(id: number): Promise<SearchResultRecord | null> {
    const result = await db.select().from(schema.searchResults)
        .where(eq(schema.searchResults.id, id));
    return result.length > 0 ? result[0] : null;
}

export async function updateSearchResult(id: number, data: Partial<Omit<SearchResultInsert, 'id' | 'createdAt'>>): Promise<SearchResultRecord | null> {
    const updateData = { ...data };
    if (updateData.confidence !== undefined) {
        updateData.confidence = String(updateData.confidence);
    }
    const result = await db.update(schema.searchResults)
        .set({ ...updateData, updatedAt: sql`now()` })
        .where(eq(schema.searchResults.id, id))
        .returning();
    return result.length > 0 ? result[0] : null;
}

export async function deleteSearchResult(id: number): Promise<boolean> {
    const result = await db.delete(schema.searchResults)
        .where(eq(schema.searchResults.id, id))
        .returning();
    return result.length > 0;
}

export async function bulkCreateSearchResults(data: Omit<SearchResultInsert, 'id' | 'createdAt' | 'updatedAt'>[]): Promise<number> {
    // Prepare the data with proper type conversions
    const processedData = data.map(item => ({
        ...item,
        confidence: item.confidence !== undefined ? String(item.confidence) : null,
    }));
    
    // Insert the records
    const result = await db.insert(schema.searchResults)
        .values(processedData)
        .returning();
    
    return result.length;
}

// --- Users (Basic - may need expansion based on auth logic) ---

export async function findUserByClerkId(clerkId: string): Promise<User | null> {
    const result = await db.select().from(schema.users).where(eq(schema.users.clerkId, clerkId));
    return result.length > 0 ? result[0] : null;
}

export async function createUser(data: Omit<UserInsert, 'id' | 'verified' | 'createdAt' | 'updatedAt'>): Promise<User> {
    const result = await db.insert(schema.users).values({
        ...data,
        verified: false, // Default verified status
    }).returning();
    return result[0];
}

export async function updateUser(id: number, data: Partial<Omit<UserInsert, 'id' | 'createdAt'>>): Promise<User | null> {
    const result = await db.update(schema.users)
        .set({ ...data, updatedAt: sql`now()` })
        .where(eq(schema.users.id, id))
        .returning();
    return result.length > 0 ? result[0] : null;
}

// --- Token Management ---

// Get a user's current token balance
export async function getUserTokens(userId: number): Promise<number> {
    const result = await db.select({ tokens: schema.users.tokens })
      .from(schema.users)
      .where(eq(schema.users.id, userId));

    return result.length > 0 ? result[0].tokens : 0;
  }

  // Add tokens to a user's balance
  export async function addUserTokens(userId: number, amount: number): Promise<User | null> {
    const result = await db.update(schema.users)
      .set({
        tokens: sql`${schema.users.tokens} + ${amount}`,
        updatedAt: sql`now()`
      })
      .where(eq(schema.users.id, userId))
      .returning();

    return result.length > 0 ? result[0] : null;
  }

  // Deduct tokens from a user's balance (returns null if user doesn't have enough tokens)
  export async function deductUserTokens(userId: number, amount: number): Promise<User | null> {
    // First, check if the user has enough tokens
    const userTokens = await getUserTokens(userId);

    if (userTokens < amount) {
      return null; // Not enough tokens
    }

    const result = await db.update(schema.users)
      .set({
        tokens: sql`${schema.users.tokens} - ${amount}`,
        updatedAt: sql`now()`
      })
      .where(eq(schema.users.id, userId))
      .returning();

    return result.length > 0 ? result[0] : null;
  }

  // Set a user's token balance to a specific value
  export async function setUserTokens(userId: number, amount: number): Promise<User | null> {
    const result = await db.update(schema.users)
      .set({
        tokens: amount,
        updatedAt: sql`now()`
      })
      .where(eq(schema.users.id, userId))
      .returning();

    return result.length > 0 ? result[0] : null;
  }

// --- Developer Shortcuts (Commented out as requested) ---

// export async function clearDatabase(): Promise<void> {
//   console.warn('DEVELOPER SHORTCUT: Clearing database tables...');
//   await db.delete(schema.uploads);
//   await db.delete(schema.guestTransactions);
//   await db.delete(schema.reportTransactions);
//   await db.delete(schema.searchReports);
//   // await db.delete(schema.userAlerts);
//   // await db.delete(schema.formHelp);
//   // await db.delete(schema.stripeUsers);
//   await db.delete(schema.users);
//   console.warn('DEVELOPER SHORTCUT: Database cleared.');
// }

// export async function seedDatabase(): Promise<void> {
//   console.warn('DEVELOPER SHORTCUT: Seeding database...');
//   // Add seeding logic here if needed
//   console.warn('DEVELOPER SHORTCUT: Database seeded.');
// }
