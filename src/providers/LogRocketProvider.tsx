'use client';

import { useEffect } from 'react';
import LogRocket from 'logrocket';
import { useUser } from '@clerk/nextjs';

// Define props interface
interface LogRocketProviderProps {
  children: React.ReactNode;
}

// Initialize LogRocket with your app ID
export function LogRocketProvider({ children }: LogRocketProviderProps) {
  const { user, isLoaded } = useUser();

  useEffect(() => {
    // Only initialize LogRocket in production
    if (process.env.NODE_ENV === 'production') {
      LogRocket.init('revefv/facetracepro');
    }
  }, []);

  // Identify user after they log in
  useEffect(() => {
    if (isLoaded && user && process.env.NODE_ENV === 'production') {
      // Prepare user data with proper type handling
      const userData: Record<string, string | number | boolean> = {
        name: user.firstName && user.lastName 
          ? `${user.firstName} ${user.lastName}`
          : user.firstName || user.username || 'Unknown User',
      };

      // Only add email if it exists
      if (user.primaryEmailAddress?.emailAddress) {
        userData.email = user.primaryEmailAddress.emailAddress;
      }

      LogRocket.identify(user.id, userData);
    }
  }, [isLoaded, user]);

  return <>{children}</>;
} 