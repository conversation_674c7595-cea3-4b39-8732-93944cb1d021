@import "tailwindcss";

@layer utilities {
  @keyframes marquee {
    /* Direction: moves right-to-left */
    0% { transform: translateX(0%); }
    100% { transform: translateX(-50%); }
  }
  .animate-marquee {
    animation: marquee 15s linear infinite; /* Faster duration */
  }
  /* Optional: Pause on hover */
  .group-hover\:pause-marquee:hover .animate-marquee {
    animation-play-state: paused;
  }
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 245, 247, 250;
  --background-end-rgb: 255, 255, 255;
  --primary: 41, 100, 221;
  --primary-dark: 34, 83, 184;
}

@theme {
  --color-primary: rgb(var(--primary));
  --color-primary-dark: rgb(var(--primary-dark));
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

[data-theme='dark'] {
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 18, 18, 18;
  --background-end-rgb: 24, 24, 24;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      rgb(var(--background-start-rgb)),
      rgb(var(--background-end-rgb))
    )
    fixed;
}
