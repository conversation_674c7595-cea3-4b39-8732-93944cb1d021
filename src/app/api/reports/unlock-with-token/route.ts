import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { 
  getSearchReportById, 
  getSearchReportByReportId,
  updateSearchReport, 
  findUserByClerkId,
  deductUserTokens
} from '@/lib/db/index';

export async function POST(req: NextRequest) {
  try {
    // Parse request body
    const { reportId } = await req.json();
    
    if (!reportId) {
      return NextResponse.json({ error: 'Report ID is required' }, { status: 400 });
    }

    // Get current user (must be signed in for token usage)
    const { userId } = await auth();
    
    if (!userId) {
      console.warn(`[unlock-with-token] Unauthorized attempt to use token for report ${reportId}`);
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    
    console.log(`[unlock-with-token] Processing token unlock for report ${reportId} by user ${userId}`);
    
    // 1. Get the report to unlock - Handle both numeric IDs and string path identifiers
    let report;
    
    // Check if reportId is a number or string path identifier
    if (/^\d+$/.test(reportId)) {
      // It's a numeric ID
      console.log(`[unlock-with-token] Using database ID ${reportId}`);
      report = await getSearchReportById(reportId);
    } else {
      // It's a string path identifier (URL path id)
      console.log(`[unlock-with-token] Using URL path ID ${reportId}`);
      report = await getSearchReportByReportId(reportId);
    }
    
    if (!report) {
      console.warn(`[unlock-with-token] Report ${reportId} not found`);
      return NextResponse.json({ error: 'Report not found' }, { status: 404 });
    }
    
    // 2. Check if report is already unlocked
    if (!report.isPrivate) {
      console.log(`[unlock-with-token] Report ${reportId} is already unlocked`);
      return NextResponse.json({ 
        success: true, 
        message: 'Report is already unlocked'
      });
    }
    
    // 3. Find the user in our database
    const user = await findUserByClerkId(userId);
    
    if (!user) {
      console.warn(`[unlock-with-token] User with clerk ID ${userId} not found in database`);
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    
    // 4. Check if user has enough tokens
    if (user.tokens < 1) {
      console.warn(`[unlock-with-token] User ${userId} has insufficient tokens (${user.tokens})`);
      return NextResponse.json({ 
        error: 'Insufficient tokens', 
        tokens: user.tokens 
      }, { status: 403 });
    }
    
    // 5. Deduct a token from the user (this function handles transaction safety)
    const updatedUser = await deductUserTokens(user.id, 1);
    
    if (!updatedUser) {
      console.error(`[unlock-with-token] Failed to deduct token from user ${userId}`);
      return NextResponse.json({ 
        error: 'Failed to deduct token', 
        tokens: user.tokens 
      }, { status: 500 });
    }
    
    // 6. Update report to unlock it
    await updateSearchReport(report.id, {
      isPrivate: false,
    });
    
    console.log(`[unlock-with-token] Successfully unlocked report ${reportId} using token for user ${userId}`);
    
    // 7. Return success with updated token count
    return NextResponse.json({
      success: true,
      message: 'Report unlocked successfully using 1 token',
      reportId: report.report_id,
      tokens: updatedUser.tokens
    });
  } catch (error) {
    console.error('[unlock-with-token] Error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ 
      error: 'Failed to unlock report',
      details: errorMessage
    }, { status: 500 });
  }
}
