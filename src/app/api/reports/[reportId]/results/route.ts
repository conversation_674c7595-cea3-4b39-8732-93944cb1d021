import { NextRequest, NextResponse } from 'next/server';
import { getSearchReportByReportId } from '@/lib/db/index';

export async function GET(
  request: NextRequest,
  { params }: { params: { reportId: string } }
) {
  try {
    // Fix: Access reportId properly by destructuring
    const { reportId } = params;
    
    if (!reportId) {
      return NextResponse.json({ error: 'Report ID is required' }, { status: 400 });
    }

    console.log(`[reports/results] Fetching results for report: ${reportId}`);
    
    // Get the report by its identifier (report_id)
    const report = await getSearchReportByReportId(reportId);
    
    if (!report) {
      console.warn(`[reports/results] Report not found: ${reportId}`);
      return NextResponse.json({ error: 'Report not found' }, { status: 404 });
    }
    
    // First check the report status - only proceed with completed reports
    if (report.status !== 'completed') {
      console.log(`[reports/results] Report ${reportId} status is ${report.status}, not ready yet`);
      
      // Return the current status with appropriate response code (202 Accepted)
      return NextResponse.json({
        status: report.status,
        progress: report.progress || 0,
        message: 'Report is still being processed',
        reportId: report.report_id,
        createdAt: report.createdAt,
        needsPolling: true
      }, { status: 202 }); // 202 Accepted = request accepted but processing not complete
    }
    
    // Now that we know the report is completed, check if it has expired
    const now = new Date();
    
    if (report.expiresAt) {
      const expiryDate = new Date(report.expiresAt);
      
      if (now > expiryDate) {
        console.log(`[reports/results] Report expired at ${expiryDate.toISOString()}: ${reportId}`);
        return NextResponse.json({ 
          error: 'Report expired', 
          message: 'This report has expired. Reports are only available for a limited time.',
          reportId: report.report_id,
          createdAt: report.createdAt,
          expiresAt: report.expiresAt.toISOString()
        }, { status: 410 }); // 410 Gone status code is appropriate for expired content
      }
      
      // Log remaining time
      const hoursRemaining = (expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60);
      console.log(`[reports/results] Report expires in ${hoursRemaining.toFixed(1)} hours: ${reportId}`);
    } else {
      // Fallback to 24 hour expiration check if expiresAt is not set
      const reportCreatedAt = new Date(report.createdAt);
      const ageInHours = (now.getTime() - reportCreatedAt.getTime()) / (1000 * 60 * 60);
      
      if (ageInHours > 24) {
        console.log(`[reports/results] Report expired (${ageInHours.toFixed(1)} hours old): ${reportId}`);
        return NextResponse.json({ 
          error: 'Report expired', 
          message: 'This report has expired. Reports are only available for 24 hours.',
          reportId: report.report_id,
          createdAt: report.createdAt,
          expiredAt: new Date(reportCreatedAt.getTime() + 24 * 60 * 60 * 1000).toISOString()
        }, { status: 410 });
      }
    }
    
    // Check if the report is private
    const isPrivate = report.isPrivate;
    
    // Parse the stored results JSON - at this point we know the report is completed and not expired
    let results = [];
    
    // Check if facecheckIdSearch field exists
    if (!report.facecheckIdSearch) {
      console.error(`[reports/results] Report ${reportId} is marked as completed but has no facecheckIdSearch data`);
      return NextResponse.json({ 
        error: 'Missing result data', 
        message: 'The report is marked as completed but has no result data',
        reportId: report.report_id
      }, { status: 500 });
    }
    
    // Try to parse the facecheckIdSearch field
    try {
      // Check if facecheckIdSearch starts with a valid JSON character
      const dataString = report.facecheckIdSearch.trim();
      let parsedResults = [];
      
      if ((dataString.startsWith('{') && dataString.endsWith('}')) || 
          (dataString.startsWith('[') && dataString.endsWith(']'))) {
        // Likely a JSON string, attempt to parse
        parsedResults = JSON.parse(dataString);
      } else {
        // Not a JSON string, might be raw data or encoded content
        console.warn(`[reports/results] facecheckIdSearch for report ${reportId} is not in JSON format despite status=completed`);
        // Return a meaningful error with details to help debugging
        return NextResponse.json({ 
          error: 'Report data format error',
          message: 'The stored results are not in the expected format. The report may need to be reprocessed.',
          reportId: report.report_id,
          status: report.status,
          dataType: typeof report.facecheckIdSearch,
          dataLength: report.facecheckIdSearch.length,
          dataPreview: report.facecheckIdSearch.substring(0, 50) + '...'
        }, { status: 500 });
      }
      
      if (Array.isArray(parsedResults)) {
        // If report is private, redact the source URLs
        if (isPrivate) {
          console.log(`[reports/results] Returning redacted results for private report: ${reportId}`);
          
          // Create a collection of domains for statistics
          const domains = new Map();
          
          // Process the results, redacting URL information
          results = parsedResults.map(result => {
            // Extract domain for stats before redacting
            try {
              const url = new URL(result.sourceUrl);
              const domain = url.hostname.replace('www.', '');
              
              if (!domains.has(domain)) {
                domains.set(domain, 1);
              } else {
                domains.set(domain, domains.get(domain) + 1);
              }
            } catch (e) {
              // Skip invalid URLs
            }
            
            // Return redacted result
            return {
              ...result,
              sourceUrl: "REDACTED", // Hide the actual URL
              title: result.title ? `${result.title.split(' ')[0]} ...` : "Source Title (Locked)",
              description: "Full information available after unlock",
              sourceType: result.sourceType || 'other',
              confidence: result.confidence, // Keep confidence score
              thumbnail: result.thumbnail, // Keep thumbnail to show the image
              domain: extractDomain(result.sourceUrl || "") // Include just the domain
            };
          });
          
          // Return locked but with redacted results
          return NextResponse.json({
            locked: true,
            message: 'This report is locked. Unlock to view full source information.',
            reportId: report.report_id,
            results: results,
            status: report.status,
            resultCount: results.length,
            domainStats: Array.from(domains.entries())
              .map(([domain, count]) => ({ domain, count }))
              .sort((a, b) => b.count - a.count)
              .slice(0, 5), // Only include top 5 domains
            createdAt: report.createdAt,
            updatedAt: report.updatedAt
          });
        } else {
          // For unlocked reports, return full results
          results = parsedResults;
        }
      }
    } catch (error) {
      // Log the beginning of the problematic string for easier debugging
      const problematicDataSnippet = report.facecheckIdSearch?.substring(0, 100) || 'Data unavailable';
      console.error(`[reports/results] Error parsing results JSON for report ${reportId}. Data snippet: "${problematicDataSnippet}..."`, error);
      
      // Return a more detailed error response with diagnostic information
      return NextResponse.json({ 
        error: 'Failed to parse report results',
        details: 'The stored results data is invalid or corrupted.',
        errorType: error instanceof Error ? error.name : 'Unknown',
        errorMessage: error instanceof Error ? error.message : String(error),
        dataPreview: report.facecheckIdSearch?.substring(0, 50) + '...' || 'Data unavailable'
      }, { status: 500 });
    }
    
    // Helper function to extract domain from URL
    function extractDomain(url: string): string {
      try {
        const domain = new URL(url).hostname.replace('www.', '');
        
        // For well-known sites, return the platform name instead of the domain
        if (domain.includes('facebook.com')) return 'Facebook';
        if (domain.includes('instagram.com')) return 'Instagram';
        if (domain.includes('twitter.com') || domain.includes('x.com')) return 'Twitter';
        if (domain.includes('linkedin.com')) return 'LinkedIn';
        if (domain.includes('tiktok.com')) return 'TikTok';
        if (domain.includes('pinterest.com')) return 'Pinterest';
        if (domain.includes('youtube.com') || domain.includes('youtu.be')) return 'YouTube';
        if (domain.includes('reddit.com')) return 'Reddit';
        
        return domain;
      } catch {
        return 'Unknown';
      }
    }
    
    // If we get here, the report is unlocked or we've handled the locked case above
    
    // For unlocked reports: return the full results
    if (!isPrivate) {
      console.log(`[reports/results] Returning full results for unlocked report: ${reportId}`);
    }
    
    // Return the full results
    return NextResponse.json({
      success: true,
      reportId: report.report_id,
      results: results,
      status: report.status,
      resultCount: results.length,
      createdAt: report.createdAt,
      updatedAt: report.updatedAt
    });
    
  } catch (error) {
    console.error('[reports/results] Error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ 
      error: 'Failed to retrieve report results',
      details: errorMessage
    }, { status: 500 });
  }
}
