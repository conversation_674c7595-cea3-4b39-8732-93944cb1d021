// src/app/r/[id]/page.tsx
'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
// Import SearchResults component with the correct path
import SearchResults from '@/app/components/SearchResults';
// Import SearchResult type from apiService
import { SearchResult } from '@/app/services/apiService';
// Import UnlockModal for locked reports
import UnlockModal from '@/components/UnlockModal';
import { useAuth } from '@clerk/nextjs';
// Import the shared ResultsDisplay component
import ResultsDisplay from '@/components/ResultsDisplay';

// Define the structure of the API response from /api/data?action=report-by-identifier
interface ReportResponse {
  id: number;
  status: 'processing' | 'completed' | 'failed';
  progress?: number;
  results?: any[]; // Results from the database
  facecheckIdSearch?: string; // JSON string with search results
  error?: string;
  createdAt?: string | Date;
  updatedAt?: string | Date;
  expiresAt?: string | Date; // When the report expires
  expiredAt?: string | Date; // Field sent when report is expired
  isPrivate?: boolean; // Whether the report is locked or not
  locked?: boolean;    // API may also send a 'locked' flag
  domainStats?: Array<{domain: string, count: number}>; // For locked preview
  resultCount?: number; // Total count for locked preview
}

// Define CSS for background pattern and gradient (enhanced from search/page.tsx)
const styles = {
  bgPattern: {
    backgroundColor: '#f7faff',
    backgroundImage: `radial-gradient(circle at 30% 0%, rgba(146, 165, 255, 0.1) 0%, rgba(146, 165, 255, 0) 50%),
    radial-gradient(circle at 70% 100%, rgba(255, 161, 191, 0.1) 0%, rgba(255, 161, 191, 0) 50%)`,
    backgroundSize: '100% 100%',
    backgroundPosition: 'center'
  },
  gridBackground: {
    backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='%2392A5FF' stroke-width='0.5' fill='none' d='M0,0 L40,0 L40,40 L0,40 L0,0 Z M10,0 L10,40 M20,0 L20,40 M30,0 L30,40 M0,10 L40,10 M0,20 L40,20 M0,30 L40,30'/%3E%3C/svg%3E")`,
    backgroundSize: '40px 40px',
  },
  // Enhanced spotlight effect with animation - more prominent radial gradient
  spotlight: {
    position: 'fixed' as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'radial-gradient(circle at 50% 40%, transparent 0%, rgba(0,0,0,0.85) 100%)',
    opacity: 0.8,
    transition: 'opacity 1s ease-in-out',
    pointerEvents: 'none' as const,
    zIndex: 1,
    animation: 'pulse 8s ease-in-out infinite alternate'
  },
  // Updated grid background pattern as requested
  geometricBg: {
    position: 'fixed' as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='%2392A5FF' stroke-width='0.5' fill='none' d='M0,0 L40,0 L40,40 L0,40 L0,0 Z M10,0 L10,40 M20,0 L20,40 M30,0 L30,40 M0,10 L40,10 M0,20 L40,20 M0,30 L40,30'/%3E%3C/svg%3E")`,
    backgroundSize: '40px 40px',
    opacity: 0.4,
    pointerEvents: 'none' as const,
    zIndex: -2
  },
  // Hexagonal pattern overlay - triangular shapes for more tech feel
  hexPatternBg: {
    position: 'fixed' as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundImage: `url("data:image/svg+xml,%3Csvg width='84' height='48' viewBox='0 0 84 48' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 0h12v6H0V0zm28 8h12v6H28V8zm14-8h12v6H42V0zm14 0h12v6H56V0zm0 8h12v6H56V8zM42 8h12v6H42V8zm0 16h12v6H42v-6zm14-8h12v6H56v-6zm14 0h12v6H70v-6zm0-16h12v6H70V0zM28 32h12v6H28v-6zM14 16h12v6H14v-6zM0 24h12v6H0v-6zm0 8h12v6H0v-6zm14 0h12v6H14v-6zm14 8h12v6H28v-6zm-14 0h12v6H14v-6zm28 0h12v6H42v-6zm14-8h12v6H56v-6zm0-8h12v6H56v-6zm14 8h12v6H70v-6zm0 8h12v6H70v-6zM14 24h12v6H14v-6zm14-8h12v6H28v-6zM14 8h12v6H14V8zM0 8h12v6H0V8z' fill='%23FFA1BF' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E")`,
    backgroundSize: '84px 48px',
    opacity: 0.3,
    pointerEvents: 'none' as const,
    zIndex: -3
  },
  // Face mesh background with subtle biometric patterns
  aiFaceBg: {
    position: 'absolute' as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundImage: `
      radial-gradient(circle at 30% 30%, rgba(146, 165, 255, 0.15) 0%, rgba(146, 165, 255, 0) 70%),
      radial-gradient(circle at 70% 70%, rgba(255, 161, 191, 0.15) 0%, rgba(255, 161, 191, 0) 70%),
      url("data:image/svg+xml,%3Csvg width='800' height='800' viewBox='0 0 800 800' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='none' stroke='%2392A5FF' stroke-width='1' stroke-opacity='0.2' d='M400,40 L250,680 M400,40 L550,680 M250,680 L550,680 M200,300 L600,300 M250,450 L550,450 M300,100 L300,300 M500,100 L500,300 M300,100 L500,100 M220,180 C220,180 300,200 400,200 C500,200 580,180 580,180'/%3E%3C/svg%3E")`,
    backgroundSize: '150% 150%',
    backgroundPosition: 'center',
    opacity: 0.4,
    pointerEvents: 'none' as const,
    zIndex: -1,
    transition: 'opacity 0.8s ease-in-out, background-size 0.8s ease-in-out'
  },
  // Triangle geometric pattern
  trianglePatternBg: {
    position: 'fixed' as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='none' stroke='%2392A5FF' stroke-width='0.6' stroke-opacity='0.15' d='M50,10 L10,90 L90,90 Z M20,30 L80,30 M30,50 L70,50 M40,70 L60,70'/%3E%3C/svg%3E")`,
    backgroundSize: '100px 100px',
    opacity: 0.4,
    pointerEvents: 'none' as const,
    zIndex: -4
  }
};

// Helper function to format expiration time
function formatExpirationTime(expiresAt: Date | string): string {
  const expiryDate = new Date(expiresAt);
  const now = new Date();
  
  // Calculate time difference in milliseconds
  const timeDiff = expiryDate.getTime() - now.getTime();
  
  if (timeDiff <= 0) {
    return "Expired";
  }
  
  // Calculate days, hours, minutes
  const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
  
  let result = "";
  if (days > 0) {
    result += `${days} day${days !== 1 ? 's' : ''}, `;
  }
  if (hours > 0 || days > 0) {
    result += `${hours} hour${hours !== 1 ? 's' : ''}, `;
  }
  result += `${minutes} minute${minutes !== 1 ? 's' : ''}`;
  
  return result;
}

// Get expiration status color with enhanced thresholds
function getExpirationColor(expiresAt: Date | string): string {
  const expiryDate = new Date(expiresAt);
  const now = new Date();
  
  // Calculate time difference in hours
  const hoursDiff = (expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60);
  
  if (hoursDiff <= 6) {
    return "text-red-500 dark:text-red-400"; // Less than 6 hours - Red #F44336
  } else if (hoursDiff <= 24) {
    return "text-yellow-500 dark:text-yellow-400"; // Less than 24 hours - Yellow #FFC107
  } else {
    return "text-green-500 dark:text-green-400"; // More than 24 hours - Green #4CAF50
  }
}

// Helper to get background class for expiration time
function getExpirationBgColor(expiresAt: Date | string): string {
  const expiryDate = new Date(expiresAt);
  const now = new Date();
  
  // Calculate time difference in hours
  const hoursDiff = (expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60);
  
  if (hoursDiff <= 6) {
    return "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800";
  } else if (hoursDiff <= 24) {
    return "bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800";
  } else {
    return "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800";
  }
}

export default function ReportPage() {
  const params = useParams();
  const router = useRouter();
  const { isLoaded: isAuthLoaded, userId } = useAuth();
  const reportIdentifier = params?.id as string; // Get reportIdentifier from URL parameter
  
  // State variables - moved from inside formatExpirationTime function
  const [reportData, setReportData] = useState<ReportResponse | null>(null);
  const [isPrivate, setIsPrivate] = useState<boolean>(false);
  const [isUnlockModalOpen, setIsUnlockModalOpen] = useState<boolean>(false);
  const [domainStats, setDomainStats] = useState<Array<{domain: string, count: number}>>([]);
  const [resultCount, setResultCount] = useState<number>(0);
  const [status, setStatus] = useState<'loading' | 'expired' | 'error' | 'completed' | 'processing'>('loading');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [progress, setProgress] = useState<number>(0);
  const [results, setResults] = useState<SearchResult[]>([]);
  const [expiresAt, setExpiresAt] = useState<string | null>(null);
  const [currentTime, setCurrentTime] = useState<Date>(new Date());
  
  // Background state
  const [backgroundFocus, setBackgroundFocus] = useState(true);
  
  // Update the timer every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000); // Update every minute
    
    return () => clearInterval(timer);
  }, []);

  // Handle unlocking the report
  const handleUnlockReport = () => {
    if (!reportData) {
      console.error('[Report Page] Cannot unlock report: reportData is undefined');
      return;
    }
    
    console.log('[Report Page] Opening unlock modal for report ID:', reportData.id);
    setIsUnlockModalOpen(true);
  };

  // Handle when unlock modal is closed
  const handleUnlockModalClose = (success: boolean) => {
    console.log(`[Report Page] Unlock modal closed with success=${success}`);
    setIsUnlockModalOpen(false);
    
    if (success) {
      console.log('[Report Page] Unlock successful, refreshing page data');
      // Refresh the page to load the unlocked data
      router.refresh();
      // Re-fetch the report data
      setTimeout(() => {
        window.location.reload();
      }, 500);
    }
  };

  // Fetch user data
  useEffect(() => {
    async function fetchUserData() {
      if (!isAuthLoaded || !userId) return;

      try {
        console.log('[Report Page] Fetching user data for authenticated user');
        const response = await fetch('/api/data?action=user-data');
        
        if (!response.ok) {
          console.error('[Report Page] Error fetching user data:', response.statusText);
          return;
        }
        
        const userData = await response.json();
        console.log('[Report Page] User data fetched:', userData);
      } catch (error) {
        console.error('[Report Page] Error fetching user data:', error);
      }
    }
    
    fetchUserData();
  }, [isAuthLoaded, userId]);

  // Fetch report data on component mount
  useEffect(() => {
    setBackgroundFocus(true);
    
    async function fetchReportData() {
      if (!reportIdentifier) return;
      
      try {
        // Use the actual results API to get report data including lock status
        const response = await fetch(`/api/reports/${reportIdentifier}/results`);
        
        if (!response.ok) {
          throw new Error(`Failed to fetch report: ${response.status}`);
        }
        
        const data: ReportResponse = await response.json();
        setReportData(data);

        // Check if the report is locked/private
        const reportIsPrivate = data.isPrivate || data.locked || false;
        setIsPrivate(reportIsPrivate);
        
        // For locked reports, store domain stats and result count for display
        if (reportIsPrivate) {
          console.log('[Report Page] Report is locked, extracting preview data');
          
          if (data.domainStats && Array.isArray(data.domainStats)) {
            setDomainStats(data.domainStats);
          }
          
          if (data.resultCount !== undefined) {
            setResultCount(data.resultCount);
          } else if (data.results && Array.isArray(data.results)) {
            // Fallback to results length if resultCount not directly provided
            setResultCount(data.results.length);
          }
        }
        
        // Save expiration date from the API response
        if (data.expiresAt) {
          setExpiresAt(data.expiresAt.toString());
          
          const expiryDate = new Date(data.expiresAt);
          const now = new Date();
          
          // Check if report has already expired
          if (now > expiryDate) {
            console.log(`Report expired on ${expiryDate}`);
            setStatus('expired');
            return;
          }
          
          console.log(`Report will expire on ${expiryDate}`);
        } else if (data.expiredAt) { // Handle the expiredAt field from expired response
          setExpiresAt(data.expiredAt.toString());
          setStatus('expired');
          return;
        }
        
        // Set progress and status from report data
        setProgress(data.progress || 0);
        setStatus(data.status === 'processing' ? 'processing' : 'completed');
        
        // Process and use facecheckIdSearch data if available
        if (data.facecheckIdSearch) {
          try {
            const storedResults = JSON.parse(data.facecheckIdSearch);
            if (Array.isArray(storedResults) && storedResults.length > 0) {
              const processedResults = storedResults.map((item: any) => ({
                id: item.id?.toString() || '',
                confidence: item.confidence || 0,
                sourceUrl: item.sourceUrl || item.url || '',
                thumbnail: item.thumbnail || '',
                title: item.title || 'Unknown',
                description: item.description || '',
                sourceType: item.sourceType || 'other'
              }));
              
              setResults(processedResults);
              setStatus('completed');
            } else if (data.status === 'processing') {
              // Keep the processing status if no results but still processing
              setStatus('processing');
            } else {
              // Completed but no results were found
              setStatus('completed');
            }
          } catch (error) {
            console.error('Error parsing report results:', error);
            setStatus('error');
            setErrorMessage('Failed to parse report data');
          }
        } else if (data.results && Array.isArray(data.results) && data.results.length > 0) {
          // Fallback to using results directly if available
          const processedResults = data.results.map((item: any) => ({
            id: item.id?.toString() || '',
            confidence: item.confidence || 0,
            sourceUrl: item.sourceUrl || item.url || '',
            thumbnail: item.thumbnail || '',
            title: item.title || 'Unknown',
            description: item.description || '',
            sourceType: item.sourceType || 'other'
          }));
          
          setResults(processedResults);
          setStatus('completed');
        } else if (data.status === 'failed') {
          setStatus('error');
          setErrorMessage(data.error || 'Report processing failed');
        } else if (data.status === 'processing') {
          setStatus('processing');
        } else {
          setStatus('completed'); // Fallback to completed with no results
        }
      } catch (error) {
        console.error('Error fetching report:', error);
        setStatus('error');
        setErrorMessage(error instanceof Error ? error.message : 'An unknown error occurred while fetching report');
      }
    }
    
    fetchReportData();
  }, [reportIdentifier]);
  
  // Function to extend report expiration time
  const handleExtendExpiration = () => {
    // This would be implemented to call a server endpoint
    alert('Extend expiration functionality will be implemented in a future update');
  };
  
  // Main container with spotlight background
  return (
    <div className="min-h-screen flex flex-col relative" style={styles.bgPattern}>
      {/* Enhanced Background Elements */}
      <div style={styles.geometricBg}></div>
      <div style={styles.hexPatternBg}></div>

      {/* Unlock Modal */}
      {reportData && (
        <UnlockModal 
          isOpen={isUnlockModalOpen} 
          onClose={handleUnlockModalClose} 
          reportId={reportData.id || 0} // Database ID 
          reportIdentifier={reportIdentifier} // URL path identifier
        />
      )}
      {/* Debug info for reportId - only visible during development */}
      {process.env.NODE_ENV === 'development' && reportData && (
        <div className="fixed bottom-2 left-2 bg-black/70 text-white text-xs p-2 rounded z-50">
          Database ID: {reportData.id || 'None'} | 
          URL ID: {reportIdentifier || 'None'}
        </div>
      )}
      <div style={{
        ...styles.aiFaceBg,
        opacity: backgroundFocus ? 0.9 : 0.4,
        backgroundSize: backgroundFocus ? '120% 120%' : '150% 150%',
      }}></div>
      
      <div style={{
        ...styles.spotlight,
        opacity: backgroundFocus ? 0.8 : 0,
        zIndex: 0
      }}></div>
      
      {/* Add CSS animation for spotlight pulse effect */}
      <style jsx global>{`
        @keyframes pulse {
          0% { opacity: 0.7; }
          50% { opacity: 0.9; }
          100% { opacity: 0.7; }
        }
      `}</style>
      
      {/* Main Content */}
      <main className="flex-1 py-5 md:py-8 relative z-10">
        <div className="container mx-auto px-4 max-w-7xl">
          {/* Expiration Timer with enhanced color-coding based on remaining time */}
          {status === 'completed' && expiresAt && (
            <div className={`border backdrop-blur-sm rounded-lg shadow-md p-4 mb-6 ${getExpirationBgColor(expiresAt)}`}>
              <div className="flex flex-col md:flex-row justify-between items-center">
                <div className="flex items-center mb-3 md:mb-0">
                  <svg className={`w-5 h-5 ${getExpirationColor(expiresAt)} mr-2`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Report Expires In:</span>
                </div>
                <div className="flex items-center">
                  <span className={`text-lg font-bold ${getExpirationColor(expiresAt)}`}>
                    {formatExpirationTime(expiresAt)}
                  </span>
                  <button
                    onClick={handleExtendExpiration}
                    className="ml-4 px-3 py-1 text-xs bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
                  >
                    Extend Time
                  </button>
                </div>
              </div>
            </div>
          )}
          
          {/* Loading State */}
          {status === 'loading' && (
            <div className="bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm rounded-xl shadow-lg overflow-hidden">
              <div className="bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] p-4">
                <div className="flex justify-between items-center">
                  <h2 className="text-xl font-bold text-white">Loading Report</h2>
                  <p className="text-sm text-white/80">ID: {reportIdentifier || 'N/A'}</p>
                </div>
              </div>
              
              <div className="p-8">
                <div className="flex flex-col items-center justify-center">
                  <div className="w-20 h-20 mb-4">
                    <svg className="animate-spin w-full h-full text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">Retrieving Your Report</h3>
                  <p className="text-gray-600 dark:text-gray-400 text-center max-w-md">
                    Please wait while we load your report data...
                  </p>
                </div>
              </div>
            </div>
          )}
          
          {/* Processing State */}
          {status === 'processing' && (
            <div className="bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm rounded-xl shadow-lg overflow-hidden">
              <div className="bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] p-4">
                <div className="flex justify-between items-center">
                  <h2 className="text-xl font-bold text-white">Processing Your Search</h2>
                  <p className="text-sm text-white/80">ID: {reportIdentifier || 'N/A'}</p>
                </div>
              </div>
              
              <div className="p-8">
                <div className="flex flex-col items-center justify-center">
                  <div className="w-20 h-20 mb-4">
                    <svg className="animate-spin w-full h-full text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">Generating Your Report</h3>
                  <p className="text-gray-600 dark:text-gray-400 text-center max-w-md mb-4">
                    Please wait while we search... This may take a minute or two.
                  </p>
                  
                  {/* Progress Bar */}
                  <div className="w-full max-w-md bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 mt-2">
                    <div className="bg-blue-600 h-2.5 rounded-full" style={{ width: `${progress}%` }}></div>
                  </div>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">{progress}% Complete</p>
                  
                  <div className="mt-6">
                    <button 
                      onClick={() => window.location.reload()}
                      className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
                    >
                      Refresh Status
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {/* Expired State */}
          {status === 'expired' && (
            <div className="bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm rounded-xl shadow-lg overflow-hidden">
              <div className="bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] p-4">
                <div className="flex justify-between items-center">
                  <h2 className="text-xl font-bold text-white">Report Expired</h2>
                  <p className="text-sm text-white/80">ID: {reportIdentifier || 'N/A'}</p>
                </div>
              </div>
              
              <div className="p-8 text-center">
                <svg className="w-20 h-20 text-yellow-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">Report No Longer Available</h3>
                <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto mb-6">
                  For privacy and security reasons, reports expire after 24 hours. Please conduct a new search if you need this information.
                </p>
                <Link 
                  href="/search"
                  className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors inline-block"
                >
                  New Search
                </Link>
              </div>
            </div>
          )}
          
          {/* Error State */}
          {status === 'error' && (
            <div className="bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm rounded-xl shadow-lg overflow-hidden">
              <div className="bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] p-4">
                <div className="flex justify-between items-center">
                  <h2 className="text-xl font-bold text-white">Error Loading Report</h2>
                  <p className="text-sm text-white/80">ID: {reportIdentifier || 'N/A'}</p>
                </div>
              </div>
              
              <div className="p-8 text-center">
                <svg className="w-20 h-20 text-red-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">Something Went Wrong</h3>
                <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto mb-1">
                  {errorMessage || 'An unexpected error occurred while loading your report.'}
                </p>
                <p className="text-gray-500 dark:text-gray-400 max-w-md mx-auto mb-6 text-sm">
                  Please try refreshing the page or starting a new search.
                </p>
                <div className="flex justify-center gap-4">
                  <button 
                    onClick={() => window.location.reload()}
                    className="px-6 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium rounded-lg transition-colors"
                  >
                    Refresh Page
                  </button>
                  <Link 
                    href="/search"
                    className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors inline-block"
                  >
                    New Search
                  </Link>
                </div>
              </div>
            </div>
          )}
          
          {/* Completed State with Results */}
          {status === 'completed' && results && results.length > 0 && (
            <div className="relative">
              {/* Navigation Bar for Results */}
              <div className="bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] rounded-t-xl shadow-md p-3 text-white mb-0">
                <div className="flex flex-wrap justify-between items-center gap-2">
                  <div className="flex items-center">
                    <div className="relative h-8 w-8 mr-2">
                      <svg className="w-full h-full" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <polygon
                          points="50,10 25,25 15,50 25,75 50,90 75,75 85,50 75,25"
                          stroke="white"
                          strokeWidth="4"
                          fill="none"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </div>
                    <span className="text-lg font-bold tracking-tight flex items-center">
                      <span className="mr-1">FaceTrace</span>
                      <span className="font-['Electrolize','SF_Mono',monospace]">.Pro</span>
                    </span>
                  </div>

                  <div className="flex items-center gap-2 overflow-x-auto pb-2 sm:pb-0">
                    <Link
                      href="/search"
                      className="px-3 py-1 rounded-md bg-white/20 text-white font-medium hover:bg-white/30 transition-colors flex items-center"
                    >
                      <svg className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                          stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                      New Search
                    </Link>

                    <div className="h-6 w-px bg-white/30"></div>
                    
                    <button
                      className="px-3 py-1 rounded-md bg-white/20 text-white font-medium hover:bg-white/30 transition-colors flex items-center"
                      onClick={() => {
                        window.print();
                      }}
                    >
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                      </svg>
                      Export PDF
                    </button>

                    <div className="h-6 w-px bg-white/30"></div>

                    <button
                      className="px-3 py-1 rounded-md bg-white/20 text-white font-medium hover:bg-white/30 transition-colors flex items-center"
                      onClick={() => alert('Removal request feature coming soon!')}
                    >
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                      Removal Request
                    </button>

                    <div className="h-6 w-px bg-white/30"></div>

                    <button
                      className="px-3 py-1 rounded-md bg-white/20 text-white font-medium hover:bg-white/30 transition-colors flex items-center"
                      onClick={() => {
                        if (confirm("Are you sure you want to delete this report? This will not delete the underlying search results.")) {
                          window.location.href = '/search';
                        }
                      }}
                    >
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                      </svg>
                      Delete Report
                    </button>
                  </div>
                </div>
              </div>
              
              {/* Only display full results when not locked/private */}
              {!isPrivate ? (
                <ResultsDisplay results={results} reportId={reportIdentifier} />
              ) : (
                <div className="bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm rounded-b-xl">
                  {/* Display redacted version of results */}
                  <ResultsDisplay 
                    results={results.map(result => ({
                      ...result,
                      sourceUrl: "REDACTED-URL", 
                      title: result.title ? `${result.title.split(' ')[0]}...` : "Source Title (Redacted)",
                      description: "Full information available after unlock",
                    }))} 
                    reportId={reportIdentifier} 
                    isRedacted={true}
                    onUnlock={handleUnlockReport}
                  />
                </div>
              )}
            </div>
          )}
          
          {/* Completed State with No Results */}
          {status === 'completed' && (!results || results.length === 0) && (
            <div className="bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm rounded-xl shadow-lg overflow-hidden">
              <div className="bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] p-4">
                <div className="flex justify-between items-center">
                  <h2 className="text-xl font-bold text-white">Search Complete</h2>
                  <p className="text-sm text-white/80">ID: {reportIdentifier || 'N/A'}</p>
                </div>
              </div>
              
              <div className="p-8 text-center">
                <svg className="w-20 h-20 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 10l4 4m0-4l-4 4"></path>
                </svg>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">No Matches Found</h3>
                <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto mb-6">
                  We couldn't find any matches for this image in our database. This could mean this face hasn't appeared online, or the matches are below our confidence threshold.
                </p>
                <Link 
                  href="/search"
                  className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors inline-block"
                >
                  Try Another Search
                </Link>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
