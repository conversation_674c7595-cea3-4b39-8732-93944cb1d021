'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import Logo from '../components/Logo';  // Import the Logo component

export default function AccessPage() {
  const [password, setPassword] = useState('');
  const [isWrong, setIsWrong] = useState(false);
  const [isShaking, setIsShaking] = useState(false);
  const router = useRouter();

  // The correct answer is "fish" - from the famous Dr<PERSON> book "One Fish, Two Fish, Red Fish, Blue Fish"
  const correctPassword = 'u64598065409845098y540';

  // Check if the user has already accessed the app in this session
  useEffect(() => {
    // Check for the access token in sessionStorage
    const accessGranted = sessionStorage.getItem('ftProAccessGranted');
    
    if (accessGranted === 'true') {
      // User has already completed the access page in this session
      router.push('/search');
    }
  }, [router]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (password.toLowerCase().trim() === correctPassword) {
      // Store access token in sessionStorage (persists for browser session only)
      sessionStorage.setItem('ftProAccessGranted', 'true');
      
      // Redirect directly to search
      router.push('/search');
    } else {
      setIsWrong(true);
      setIsShaking(true);
      setTimeout(() => setIsShaking(false), 500);
      setTimeout(() => setIsWrong(false), 2000);
    }
  };

  // Show the riddle
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-b from-blue-50 to-blue-100 p-4">
      {/* Logo Above Card */}
      <div className="mb-8 text-center">
        <Logo vertical={true} />
      </div>
      
      <div className="max-w-md w-full bg-white rounded-2xl shadow-lg overflow-hidden">
        <motion.div 
          className="p-8"
          animate={isShaking ? { x: [-10, 10, -10, 10, 0] } : {}}
          transition={{ duration: 0.5 }}
        >
          <div className="text-center mb-6">
            <p className="text-gray-600">Please solve the riddle to enter</p>
          </div>

          <div className="p-6 bg-blue-100 rounded-xl mb-6 text-center">
            <p className="text-lg text-blue-800 font-['Comic_Sans_MS',cursive] leading-relaxed">
              One is red and one is blue,<br/>
              Two swim together, that is true.<br/>
              In a famous tale that children know,<br/>
              What am I? Please tell me so!
            </p>
          </div>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <input
                type="text"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className={`w-full px-4 py-3 rounded-full border ${isWrong ? 'border-red-500' : 'border-gray-300'} focus:outline-none focus:ring-2 focus:ring-blue-500`}
                placeholder="Enter your answer..."
                autoComplete="off"
              />
              {isWrong && (
                <p className="text-red-500 text-sm mt-2">Oh no! That's not right. Try again!</p>
              )}
            </div>
            
            <button
              type="submit"
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-full transition-colors duration-200"
            >
              Enter the Wonderland
            </button>
          </form>
          
          <div className="mt-6 text-center text-sm text-gray-500">
            <p>ask bryce for access</p>
          </div>
        </motion.div>
      </div>

      <div className="mt-8 text-center">
        <p className="text-sm text-gray-500">© {new Date().getFullYear()} FaceTrace Pro. All rights reserved.</p>
      </div>
    </div>
  );
} 