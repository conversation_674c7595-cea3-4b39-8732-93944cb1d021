'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { motion, useReducedMotion } from 'framer-motion';
import Logo from './components/Logo';

export default function Home() {
  const router = useRouter(); 
  const [isLoading, setIsLoading] = useState(true);
  const prefersReducedMotion = useReducedMotion();
  const [isMobile, setIsMobile] = useState(false);

  // Check for mobile screen size on client side only
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 640);
    };
    
    // Initial check
    checkMobile();
    
    // Add resize listener
    window.addEventListener('resize', checkMobile);
    
    // Cleanup
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Redirect to search page after a short delay, but only if not coming from a Clerk auth redirect
  useEffect(() => {

    // Increase the delay to allow the loader animation to be visible longer
    const redirectTimer = setTimeout(() => {
      router.push('/access');
    }, 1000); // 1 second delay for loading animation to be visible

    return () => clearTimeout(redirectTimer);

    /*
    // Check if this is a return from Clerk authentication
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const isClerkRedirect = urlParams.get('clerk_redirect') || 
                             urlParams.get('redirect_url') || 
                             urlParams.has('__clerk_status');
      
      // Only redirect if this is not a Clerk auth redirect
      if (!isClerkRedirect) {
        const redirectTimer = setTimeout(() => {
          router.push('/search');
        }, 500); // 500ms delay for loading animation
        
        return () => clearTimeout(redirectTimer);
      } else {
        // If coming from Clerk auth, preserve any auth parameters and redirect immediately
        const currentParams = window.location.search;
        router.push(`/search${currentParams}`);
      }
    }
      */
  }, [router]);

  // Define CSS for background pattern and gradient
  const styles = {
    bgPattern: {
      backgroundColor: '#f7faff',
      backgroundImage: `radial-gradient(circle at 30% 0%, rgba(146, 165, 255, 0.1) 0%, rgba(146, 165, 255, 0) 50%), 
      radial-gradient(circle at 70% 100%, rgba(255, 161, 191, 0.1) 0%, rgba(255, 161, 191, 0) 50%)`,
      backgroundSize: '100% 100%',
      backgroundPosition: 'center'
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 to-white dark:from-gray-900 dark:to-gray-800 relative overflow-hidden" style={styles.bgPattern}>
      {/* Background Gradient Overlay */}
      <div className="absolute inset-0 pointer-events-none z-0 opacity-30" 
        style={{
          backgroundImage: `radial-gradient(circle at 30% 0%, rgba(146, 165, 255, 0.1) 0%, rgba(146, 165, 255, 0) 50%), 
            radial-gradient(circle at 70% 100%, rgba(255, 161, 191, 0.1) 0%, rgba(255, 161, 191, 0) 50%)`,
          backgroundSize: '100% 100%',
          backgroundPosition: 'center'
        }}
      />
      
      {/* Decorative elements */}
      <div className="absolute top-0 right-0 w-72 h-72 bg-blue-200/20 rounded-full -mr-36 -mt-36 blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-blue-300/10 rounded-full -ml-32 -mb-32 blur-3xl"></div>
      
      <main className="flex-1 flex items-center justify-center min-h-[100svh] w-full relative z-10">
        <div className="w-full max-w-md mx-auto px-4">
          <motion.div 
            className="text-center p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            {/* Loading Animation with Logo */}
            <div className="flex flex-col items-center justify-center">
              <motion.div 
                className="mb-6 origin-center"
                style={{ 
                  scale: isMobile ? 1.2 : 1.4
                }}
                animate={!prefersReducedMotion ? 
                  { scale: isMobile ? [1.2, 1.3, 1.2] : [1.4, 1.5, 1.4] }
                : {}}
                transition={{ 
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut" 
                }}
              >
                <Logo className="pointer-events-none" vertical={true} />
              </motion.div>
              
              {/* Loading Bar */}
              <div className="w-48 h-1 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden mt-4">
                <motion.div 
                  className="h-full bg-gradient-to-r from-blue-500 to-blue-600"
                  initial={{ width: "0%" }}
                  animate={{ width: "100%" }}
                  transition={{ 
                    duration: 0.7,
                    ease: "easeInOut"
                  }}
                />
              </div>
              
              <motion.p 
                className="mt-4 text-sm text-gray-600 dark:text-gray-400 font-medium"
                initial={!prefersReducedMotion ? { opacity: 0 } : { opacity: 1 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2, duration: 0.3 }}
              >
                Preparing access verification...
              </motion.p>
            </div>
          </motion.div>
        </div>
      </main>
    </div>
  );
}
