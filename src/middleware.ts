import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";

// Define public routes that don't require authentication
const isPublicRoute = createRouteMatcher([
  // Allow search page as public for guest usage
  '/',
  '/search',
  '/r/:path*', // Report viewing routes
  // Static pages
  '/about',
  '/contact',
  '/legal',
  '/privacy-policy',
  '/terms-of-use',
  '/faq',
  '/dmca-takedown',
  '/opt-out',
  '/refund-policy',
  '/report-bug',
  '/sar-form',
  '/support',
  // API routes that handle their own auth
  '/api/webhooks/:path*', // Webhook endpoints
  '/api/start-search',    // Search start endpoint
  '/api/search',          // Search progress/results endpoint
  // Static assets and resources
  '/_next/static/:path*',
  '/images/:path*',
  '/api/health', // Health check endpoint
]);

// Enhanced clerk middleware with custom logic
export default clerkMiddleware((auth, req) => {
  // Check if this is a public route
  if (isPublicRoute(req)) {
    return NextResponse.next();
  }

  // Let Clerk handle the auth for all routes
  // No need to manually check auth status here as Clerk will do this
  return NextResponse.next();
});

// This configuration ensures the middleware runs on the specified paths
// The negative lookahead pattern excludes certain paths from the middleware
export const config = {
  matcher: [
    // Skip Next.js internals, Clerk auth pages, and static files
    '/((?!_next|_clerk|clerk-proxy|sign-in|sign-up|images|favicon|api/webhooks).*)',
    // Always run middleware for API routes except webhooks
    '/(api(?!/webhooks))(.*)',
  ],
};
